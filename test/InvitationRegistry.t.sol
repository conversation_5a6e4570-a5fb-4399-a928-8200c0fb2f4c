// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "../src/InvitationRegistry.sol";
import "../src/UserVault.sol";

contract InvitationRegistryTest is Test {
    InvitationRegistry public registry;
    address public rootUser;
    address public user1;
    address public user2;
    address public user3;

    function setUp() public {
        rootUser = makeAddr("rootUser");
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");
        user3 = makeAddr("user3");
        
        registry = new InvitationRegistry(rootUser);
    }

    function testInitialState() public {
        assertEq(registry.rootUser(), rootUser);
        assertEq(registry.totalUsers(), 1); // Root user is automatically registered
        assertNotEq(registry.userVaults(rootUser), address(0));
        assertEq(registry.inviterOf(rootUser), address(0));
    }

    function testRegisterWithRootUser() public {
        vm.prank(user1);
        address vault = registry.register(rootUser);
        
        assertNotEq(vault, address(0));
        assertEq(registry.userVaults(user1), vault);
        assertEq(registry.inviterOf(user1), rootUser);
        assertEq(registry.totalUsers(), 2);
    }

    function testRegisterWithValidInviter() public {
        // First register user1 with root
        vm.prank(user1);
        registry.register(rootUser);
        
        // Then register user2 with user1
        vm.prank(user2);
        address vault = registry.register(user1);
        
        assertNotEq(vault, address(0));
        assertEq(registry.userVaults(user2), vault);
        assertEq(registry.inviterOf(user2), user1);
        assertEq(registry.totalUsers(), 3);
    }

    function testCannotRegisterTwice() public {
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user1);
        vm.expectRevert(InvitationRegistry.AlreadyRegistered.selector);
        registry.register(rootUser);
    }

    function testCannotSelfInvite() public {
        vm.prank(user1);
        vm.expectRevert(InvitationRegistry.CannotSelfInvite.selector);
        registry.register(user1);
    }

    function testCannotRegisterWithInvalidInviter() public {
        vm.prank(user1);
        vm.expectRevert(InvitationRegistry.InvalidInviter.selector);
        registry.register(user2); // user2 is not registered
    }

    function testPreventDuplicateInvitation() public {
        // Register user1
        vm.prank(user1);
        registry.register(rootUser);

        // Register user2 with user1 as inviter
        vm.prank(user2);
        registry.register(user1);

        // Try to register user2 again (should fail)
        vm.prank(user2);
        vm.expectRevert("AlreadyRegistered");
        registry.register(user1);

        // Check canInvite function
        assertTrue(registry.canInvite(user1, user3));  // Can invite user3
        assertFalse(registry.canInvite(user1, user2)); // Cannot invite user2 again
        assertFalse(registry.canInvite(user1, user1)); // Cannot self-invite
    }

    function testGetInvitationChain() public {
        // Create a chain: rootUser -> user1 -> user2 -> user3
        vm.prank(user1);
        registry.register(rootUser);
        
        vm.prank(user2);
        registry.register(user1);
        
        vm.prank(user3);
        registry.register(user2);
        
        address[] memory chain = registry.getInvitationChain(user3);
        
        assertEq(chain.length, 4);
        assertEq(chain[0], user3);
        assertEq(chain[1], user2);
        assertEq(chain[2], user1);
        assertEq(chain[3], rootUser);
    }

    function testVaultFunctionality() public {
        vm.prank(user1);
        address vaultAddr = registry.register(rootUser);

        UserVault vault = UserVault(vaultAddr);

        assertEq(vault.owner(), user1);
        assertEq(vault.inviter(), rootUser);
        assertEq(vault.registry(), address(registry));  // 验证registry地址正确设置
        assertTrue(vault.initialized());
        assertEq(vault.totalInvites(), 0);

        // Register user2 with user1 as inviter
        vm.prank(user2);
        registry.register(user1);

        // Check that user1's vault recorded the invitation
        assertEq(vault.totalInvites(), 1);
        assertTrue(vault.hasInvited(user2));  // 检查防重复邀请标记
        assertEq(vault.getCurrentDailyInvites(), 1);  // 检查每日邀请数
    }

    function testComputeVaultAddress() public {
        address predicted = registry.computeVaultAddress(user1);
        
        vm.prank(user1);
        address actual = registry.register(rootUser);
        
        assertEq(predicted, actual);
    }

    function testVaultPermissionControl() public {
        vm.prank(user1);
        address vaultAddr = registry.register(rootUser);

        UserVault vault = UserVault(vaultAddr);

        // 只有Registry可以调用recordInvitation
        vm.prank(user2);  // 非Registry地址
        vm.expectRevert(UserVault.Unauthorized.selector);
        vault.recordInvitation(user3);

        // Registry可以正常调用
        vm.prank(address(registry));
        vault.recordInvitation(user3);  // 应该成功

        assertEq(vault.totalInvites(), 1);
    }

    function testDailyInviteTracking() public {
        vm.prank(user1);
        address vaultAddr = registry.register(rootUser);

        UserVault vault = UserVault(vaultAddr);

        // Register user2 and user3 on the same day
        vm.prank(user2);
        registry.register(user1);

        vm.prank(user3);
        registry.register(user1);

        assertEq(vault.totalInvites(), 2);
        assertEq(vault.getCurrentDailyInvites(), 2);

        // Simulate next day (advance 1 day)
        vm.warp(block.timestamp + 1 days);

        // Daily invites should reset to 0 for new day
        assertEq(vault.getCurrentDailyInvites(), 0);
        assertEq(vault.totalInvites(), 2); // Total should remain the same
    }

    function testRegistrationPause() public {
        // Pause registration
        registry.pauseRegistration();

        // Try to register (should fail)
        vm.prank(user1);
        vm.expectRevert("Registration paused");
        registry.register(rootUser);

        // Unpause registration
        registry.unpauseRegistration();

        // Now registration should work
        vm.prank(user1);
        address vault = registry.register(rootUser);
        assertNotEq(vault, address(0));
    }
}
